# ตัวอย่างข้อมูลการหมดอายุบัตรประชาชน

## ข้อมูลที่เพิ่มเติมในการอ่านบัตร

เมื่อทำการอ่านบัตรประชาชน ระบบจะเพิ่มข้อมูลการหมดอายุบัตรในส่วน `cardExpiry` ดังนี้:

### กรณีบัตรยังไม่หมดอายุ

```json
{
  "citizenId": "1234567890123",
  "titleTH": "นาย",
  "firstNameTH": "สมชาย",
  "lastNameTH": "ใจดี",
  // ... ข้อมูลอื่นๆ ...
  "expireDate": "15/08/2567",
  "cardExpiry": {
    "status": "valid",
    "message": "บัตรเหลืออายุอีก 1 ปี 2 เดือน 28 วัน",
    "years": 1,
    "months": 2,
    "days": 28,
    "daysRemaining": 423,
    "isExpired": false,
    "isNearExpiry": false,
    "expireDate": "2024-08-15"
  }
}
```

### กรณีบัตรใกล้หมดอายุ (เหลือไม่เกิน 6 เดือน)

```json
{
  "cardExpiry": {
    "status": "valid",
    "message": "บัตรเหลืออายุอีก 0 ปี 3 เดือน 15 วัน",
    "years": 0,
    "months": 3,
    "days": 15,
    "daysRemaining": 105,
    "isExpired": false,
    "isNearExpiry": true,
    "expireDate": "2024-08-15"
  }
}
```

### กรณีบัตรหมดอายุแล้ว

```json
{
  "cardExpiry": {
    "status": "expired",
    "message": "บัตรหมดอายุแล้ว 1 ปี 2 เดือน 10 วัน",
    "years": 1,
    "months": 2,
    "days": 10,
    "daysRemaining": -437,
    "isExpired": true,
    "isNearExpiry": false,
    "expireDate": "2023-01-15"
  }
}
```

### กรณีบัตรไม่มีวันหมดอายุ (99/99/9999)

```json
{
  "cardExpiry": {
    "status": "never_expires",
    "message": "บัตรไม่มีวันหมดอายุ",
    "daysRemaining": null,
    "isExpired": false,
    "isNearExpiry": false
  }
}
```

## ข้อมูลใน Log

เมื่อทำการอ่านบัตร ระบบจะแสดงข้อมูลการหมดอายุใน log ดังนี้:

### บัตรปกติ
```
[SUCCESS] อ่านข้อมูลสำเร็จ: นาย#สมชาย##ใจดี
[INFO] บัตรเหลืออายุอีก 1 ปี 2 เดือน 28 วัน
```

### บัตรใกล้หมดอายุ
```
[SUCCESS] อ่านข้อมูลสำเร็จ: นาย#สมชาย##ใจดี
[INFO] บัตรเหลืออายุอีก 0 ปี 3 เดือน 15 วัน
[WARN] บัตรใกล้หมดอายุ (เหลือ 105 วัน) กรุณาเตรียมต่ออายุบัตร
```

### บัตรหมดอายุแล้ว
```
[SUCCESS] อ่านข้อมูลสำเร็จ: นาย#สมชาย##ใจดี
[INFO] บัตรหมดอายุแล้ว 1 ปี 2 เดือน 10 วัน
[WARN] บัตรหมดอายุแล้ว! กรุณาติดต่อสำนักงานเขตเพื่อต่ออายุบัตร
```

### บัตรไม่มีวันหมดอายุ
```
[SUCCESS] อ่านข้อมูลสำเร็จ: นาย#สมชาย##ใจดี
[INFO] บัตรไม่มีวันหมดอายุ
```

## คุณสมบัติของข้อมูลการหมดอายุ

### ฟิลด์ที่สำคัญ:

- **status**: สถานะของบัตร (`valid`, `expired`, `never_expires`)
- **message**: ข้อความแสดงสถานะเป็นภาษาไทย
- **years, months, days**: จำนวนปี เดือน วัน ที่เหลือหรือเกินมา
- **daysRemaining**: จำนวนวันที่เหลือ (ค่าลบหมายถึงหมดอายุแล้ว)
- **isExpired**: บัตรหมดอายุหรือไม่ (true/false)
- **isNearExpiry**: บัตรใกล้หมดอายุหรือไม่ (เหลือไม่เกิน 6 เดือน)
- **expireDate**: วันหมดอายุในรูปแบบ ISO (YYYY-MM-DD)

### การใช้งาน:

1. **ตรวจสอบสถานะบัตร**: ใช้ `cardExpiry.status` หรือ `cardExpiry.isExpired`
2. **แจ้งเตือนบัตรใกล้หมดอายุ**: ใช้ `cardExpiry.isNearExpiry`
3. **แสดงข้อความ**: ใช้ `cardExpiry.message`
4. **คำนวณวันที่เหลือ**: ใช้ `cardExpiry.daysRemaining`

## ประโยชน์ของการเพิ่มข้อมูลนี้:

1. **การแจ้งเตือน**: สามารถแจ้งเตือนผู้ใช้เมื่อบัตรใกล้หมดอายุ
2. **การตรวจสอบ**: ป้องกันการใช้บัตรที่หมดอายุแล้ว
3. **การวางแผน**: ช่วยให้ผู้ใช้วางแผนต่ออายุบัตรล่วงหน้า
4. **การรายงาน**: สามารถสร้างรายงานสถิติบัตรที่หมดอายุ
5. **การจัดการข้อมูล**: ช่วยในการจัดการฐานข้อมูลผู้ป่วยหรือลูกค้า
