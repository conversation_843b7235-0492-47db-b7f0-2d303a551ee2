    // preload.js
    const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

    contextBridge.exposeInMainWorld('electronAPI', {
        // Main to Renderer (รับข้อมูลจาก Backend)
        onLog: (callback) => ipcRenderer.on('log', (_event, value) => callback(value)),
        onStatusUpdate: (callback) => ipcRenderer.on('status-update', (_event, value) => callback(value)),

        // Renderer to Main (ส่งคำสั่งไป Backend)
        getConfig: () => ipcRenderer.invoke('get-config'),
        saveConfig: (config) => ipcRenderer.invoke('save-config', config)
    });
    