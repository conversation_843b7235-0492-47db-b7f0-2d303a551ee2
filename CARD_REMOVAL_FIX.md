# การแก้ไขปัญหา "Cannot read properties of undefined (reading 'toString')"

## ปัญหาที่เกิดขึ้น

เมื่อผู้ใช้ถอดบัตรประชาชนออกจากเครื่องอ่านบัตรแล้วเสียบใหม่ โปรแกรมจะเกิด error:

```
[ERROR] Uncaught Exception: Cannot read properties of undefined (reading 'toString')
[DEBUG] Stack: TypeError: Cannot read properties of undefined (reading 'toString') at new ResponseApdu (C:\code\nodejs\actse-hardware-bridge\node_modules\smartcard\lib\ResponseApdu.js:56:24) at C:\code\nodejs\actse-hardware-bridge\node_modules\smartcard\lib\Card.js:102:23
```

## สาเหตุของปัญหา

1. **Smartcard Library Issue**: เมื่อบัตรถูกถอดออกระหว่างการอ่าน smartcard library จะส่ง `response` ที่เป็น `undefined` หรือ `null` ไปยัง `ResponseApdu` constructor
2. **ResponseApdu Constructor**: พยายามเรียก `.toString('hex')` บน buffer ที่ไม่มีอยู่
3. **Lack of Error Handling**: ไม่มีการตรวจสอบและจัดการ error ที่เหมาะสม

## การแก้ไขที่ทำไป

### 1. ปรับปรุงฟังก์ชัน `issueCommand`

```javascript
const issueCommand = (commandBytes) => new Promise((resolve, reject) => {
    try {
        // Check if card is still valid before issuing command
        if (!card) {
            return reject(new Error('Card object is null or undefined'));
        }

        card.issueCommand(new CommandApdu({
            bytes: commandBytes
        }), (err, res) => {
            if (err) {
                const errorMessage = err.message || err.toString() || 'Unknown card error';
                return reject(new Error(`Card command error: ${errorMessage}`));
            }

            // Check for undefined or null response
            if (res === undefined || res === null) {
                return reject(new Error('Card response is null or undefined - card may have been removed'));
            }

            // Validate response format
            if (!Buffer.isBuffer(res)) {
                if (res.buffer && Buffer.isBuffer(res.buffer)) {
                    resolve(res.buffer);
                } else {
                    return reject(new Error('Invalid card response format - not a buffer'));
                }
            } else {
                resolve(res);
            }
        });
    } catch (e) {
        const errorMessage = e.message || e.toString() || 'Unknown execution error';
        reject(new Error(`Command execution error: ${errorMessage}`));
    }
});
```

### 2. เพิ่ม Retry Mechanism ในฟังก์ชัน `getData`

```javascript
const getData = async (requestApdu, retryCount = 0) => {
    try {
        // ... existing code ...
    } catch (error) {
        const errorMessage = error.message || error.toString() || 'Unknown getData error';
        
        // Check if error indicates card removal
        if (errorMessage.includes('null') || 
            errorMessage.includes('undefined') || 
            errorMessage.includes('removed')) {
            throw new Error(`Card was removed during operation: ${errorMessage}`);
        }
        
        // Retry logic for transient errors
        if (retryCount < 2 && !errorMessage.includes('removed')) {
            sendToUI('log', `[DEBUG] Retrying command (attempt ${retryCount + 1}): ${errorMessage}`);
            await new Promise(resolve => setTimeout(resolve, 100)); // Small delay
            return getData(requestApdu, retryCount + 1);
        }
        
        throw new Error(`getData error: ${errorMessage}`);
    }
};
```

### 3. ปรับปรุงการจัดการ Error ใน Card-Inserted Event

```javascript
event.device.on('card-inserted', async (cardEvent) => {
    try {
        // Add a small delay to ensure card is properly initialized
        await new Promise(resolve => setTimeout(resolve, 500));
        
        await Promise.race([
            readThaiIDData(cardEvent.card),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Card reading timeout')), 30000))
        ]);
    } catch (e) {
        // Check if error is related to card removal
        const errorMessage = e.message || e.toString() || 'Unknown error';
        if (errorMessage.includes('undefined') || 
            errorMessage.includes('null') || 
            errorMessage.includes('removed') ||
            errorMessage.includes('Card response is null')) {
            sendToUI('log', `[WARN] Card was removed during reading: ${errorMessage}`);
            sendToUI('status-update', {
                type: 'card',
                status: 'removed'
            });
            setCurrentCardStatus('removed');
            sendMessageToVps('card_removed', {});
        } else {
            // Handle other errors normally
            // ... error handling code ...
        }
    }
});
```

### 4. ปรับปรุง Global Error Handling

```javascript
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    const errorMessage = error.message || error.toString() || 'Unknown uncaught exception';
    
    if (mainWindow) {
        sendToUI('log', `[ERROR] Uncaught Exception: ${errorMessage}`);
        sendToUI('log', `[DEBUG] Stack: ${error.stack || 'No stack trace available'}`);
        
        // Check if error is related to smartcard operations
        if (errorMessage.includes('ResponseApdu') || 
            errorMessage.includes('toString') || 
            errorMessage.includes('smartcard') ||
            errorMessage.includes('Card.js')) {
            sendToUI('log', '[INFO] Smart card error detected, attempting to restart service...');
            
            // Try to restart smartcard service
            setTimeout(() => {
                restartSmartcardService();
            }, 2000);
        }
    }
});
```

### 5. เพิ่มฟังก์ชัน Restart Service

```javascript
function restartSmartcardService() {
    sendToUI('log', '[INFO] กำลังรีสตาร์ทบริการ Smart Card...');
    
    try {
        // Clean up existing service
        cleanupSmartcardService();
        
        // Wait a moment before restarting
        setTimeout(() => {
            try {
                startSmartcardService();
                sendToUI('log', '[INFO] รีสตาร์ทบริการ Smart Card สำเร็จ');
            } catch (restartError) {
                const errorMessage = restartError.message || restartError.toString() || 'Unknown restart error';
                sendToUI('log', `[ERROR] ไม่สามารถรีสตาร์ทบริการ Smart Card ได้: ${errorMessage}`);
            }
        }, 1000);
    } catch (error) {
        const errorMessage = error.message || error.toString() || 'Unknown restart preparation error';
        sendToUI('log', `[ERROR] เกิดข้อผิดพลาดในการเตรียมรีสตาร์ท: ${errorMessage}`);
    }
}
```

## ผลลัพธ์ของการแก้ไข

1. **ป้องกัน Crash**: โปรแกรมจะไม่ crash เมื่อเกิด error จากการถอดบัตรระหว่างการอ่าน
2. **Auto Recovery**: ระบบจะพยายาม restart smartcard service อัตโนมัติเมื่อเกิด error
3. **Better Error Handling**: มีการจัดการ error ที่ดีขึ้นและแสดงข้อความที่เข้าใจง่าย
4. **Retry Mechanism**: มีการลองใหม่สำหรับ error ชั่วคราว
5. **Card State Management**: จัดการสถานะของบัตรได้ดีขึ้น

## การทดสอบ

1. เปิดโปรแกรม
2. เสียบบัตรประชาชน - ควรอ่านได้ปกติ
3. ถอดบัตรออก - ควรแสดงสถานะ "card_removed"
4. เสียบบัตรใหม่ - ควรอ่านได้โดยไม่เกิด error
5. ทำซ้ำหลายครั้ง - ควรทำงานได้เสถียร

## หมายเหตุ

การแก้ไขนี้จะช่วยให้โปรแกรมทำงานได้เสถียรขึ้นเมื่อมีการถอดและเสียบบัตรซ้ำๆ โดยไม่ต้องรีสตาร์ทโปรแกรม
