// fix-native-deps.js - Fix native dependencies for Electron
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('ACTSE Hardware Bridge - Native Dependencies Fixer');
console.log('=================================================');

// Get system information
const os = require('os');
console.log(`Platform: ${os.platform()}`);
console.log(`Architecture: ${os.arch()}`);
console.log(`Node.js version: ${process.version}`);

// Check Electron version
try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const electronVersion = packageJson.devDependencies?.electron || packageJson.dependencies?.electron;
    console.log(`Electron version: ${electronVersion}`);
} catch (error) {
    console.log('Could not determine Electron version');
}

console.log('\n--- Fixing Native Dependencies ---');

// Step 1: Clean existing builds
console.log('1. Cleaning existing native builds...');
try {
    const nodeModulesPath = path.join(__dirname, 'node_modules', '@pokusew', 'pcsclite');
    const buildPath = path.join(nodeModulesPath, 'build');
    
    if (fs.existsSync(buildPath)) {
        fs.rmSync(buildPath, { recursive: true, force: true });
        console.log('   ✓ Cleaned @pokusew/pcsclite build directory');
    }
} catch (error) {
    console.log('   ⚠ Could not clean build directory:', error.message);
}

// Step 2: Rebuild with electron-rebuild
console.log('\n2. Rebuilding with electron-rebuild...');
try {
    execSync('npx electron-rebuild', { stdio: 'inherit' });
    console.log('   ✓ electron-rebuild completed');
} catch (error) {
    console.log('   ✗ electron-rebuild failed:', error.message);
    
    // Step 3: Try alternative method
    console.log('\n3. Trying alternative rebuild method...');
    try {
        execSync('npm run postinstall', { stdio: 'inherit' });
        console.log('   ✓ postinstall rebuild completed');
    } catch (error2) {
        console.log('   ✗ postinstall rebuild failed:', error2.message);
        
        // Step 4: Manual rebuild
        console.log('\n4. Trying manual rebuild...');
        try {
            execSync('npx electron-builder install-app-deps', { stdio: 'inherit' });
            console.log('   ✓ Manual rebuild completed');
        } catch (error3) {
            console.log('   ✗ Manual rebuild failed:', error3.message);
            showTroubleshootingTips();
            process.exit(1);
        }
    }
}

// Step 5: Verify the fix
console.log('\n--- Verifying Fix ---');
const bindingPaths = [
    'node_modules/@pokusew/pcsclite/build/Release/pcsclite.node',
    'node_modules/@pokusew/pcsclite/build/Debug/pcsclite.node',
    'node_modules/@pokusew/pcsclite/lib/binding/node-v110-darwin-arm64/pcsclite.node'
];

let foundBinding = false;
for (const bindingPath of bindingPaths) {
    const fullPath = path.join(__dirname, bindingPath);
    if (fs.existsSync(fullPath)) {
        console.log(`✓ Found native binding: ${bindingPath}`);
        const stats = fs.statSync(fullPath);
        console.log(`  Size: ${stats.size} bytes`);
        console.log(`  Modified: ${stats.mtime}`);
        foundBinding = true;
        break;
    }
}

if (foundBinding) {
    console.log('\n✅ Native dependencies appear to be fixed!');
    console.log('\nYou can now try:');
    console.log('  npm start           - Start the application');
    console.log('  npm run build:win   - Build Windows installer');
} else {
    console.log('\n❌ Native binding still not found');
    showTroubleshootingTips();
}

function showTroubleshootingTips() {
    console.log('\n--- Troubleshooting Tips ---');
    console.log('\n1. Install build tools:');
    
    if (os.platform() === 'darwin') {
        console.log('   xcode-select --install');
    } else if (os.platform() === 'win32') {
        console.log('   npm install --global windows-build-tools');
    } else {
        console.log('   sudo apt-get install build-essential');
    }
    
    console.log('\n2. Clear npm cache:');
    console.log('   npm cache clean --force');
    
    console.log('\n3. Reinstall dependencies:');
    console.log('   rm -rf node_modules package-lock.json');
    console.log('   npm install');
    
    console.log('\n4. Check Node.js/Electron compatibility:');
    console.log('   - Ensure Node.js version is compatible with Electron');
    console.log('   - Try downgrading Electron if needed');
    
    console.log('\n5. Platform-specific issues:');
    if (os.platform() === 'darwin' && os.arch() === 'arm64') {
        console.log('   - macOS ARM64 (M1/M2): Some native modules may need Rosetta');
        console.log('   - Try: arch -x86_64 npm install');
    }
    
    console.log('\n6. Alternative smart card library:');
    console.log('   - Consider using a different smart card library');
    console.log('   - Check if @pokusew/pcsclite supports your platform');
}
