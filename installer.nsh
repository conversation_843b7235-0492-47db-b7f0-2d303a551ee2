; installer.nsh - Custom NSIS installer script for ACTSE Hardware Bridge
; This script adds auto-startup functionality and custom installation options

!include "MUI2.nsh"
!include "FileFunc.nsh"

; Custom installer pages and functions
!define MUI_CUSTOMFUNCTION_GUIINIT onGUIInit
!define M<PERSON>_CUSTOMFUNCTION_UNGUIINIT un.onGUIInit

; Variables for auto-startup option
Var AutoStartup
Var AutoStartupCheckbox

; Custom page for auto-startup option
Page custom AutoStartupPage AutoStartupPageLeave

; Function to create auto-startup page
Function AutoStartupPage
    !insertmacro MUI_HEADER_TEXT "Startup Options" "Configure ACTSE Hardware Bridge startup behavior"
    
    nsDialogs::Create 1018
    Pop $0
    
    ${If} $0 == error
        Abort
    ${EndIf}
    
    ; Create checkbox for auto-startup
    ${NSD_CreateCheckBox} 20 20 300 15 "&Start ACTSE Hardware Bridge automatically when Windows starts"
    Pop $AutoStartupCheckbox
    
    ; Set default to checked
    ${NSD_Check} $AutoStartupCheckbox
    
    ; Create description text
    ${NSD_CreateLabel} 20 50 350 30 "This will add ACTSE Hardware Bridge to Windows startup programs so it starts automatically when you log in to Windows."
    Pop $0
    
    ${NSD_CreateLabel} 20 90 350 30 "You can change this setting later in the application preferences or Windows startup settings."
    Pop $0
    
    nsDialogs::Show
FunctionEnd

; Function to handle auto-startup page leave
Function AutoStartupPageLeave
    ${NSD_GetState} $AutoStartupCheckbox $AutoStartup
FunctionEnd

; Function called when installer GUI initializes
Function onGUIInit
    ; Set installer window title
    SendMessage $HWNDPARENT ${WM_SETTEXT} 0 "STR:ACTSE Hardware Bridge Setup"
FunctionEnd

; Function called when uninstaller GUI initializes
Function un.onGUIInit
    ; Set uninstaller window title
    SendMessage $HWNDPARENT ${WM_SETTEXT} 0 "STR:Uninstall ACTSE Hardware Bridge"
FunctionEnd

; Custom function to run after installation
Function .onInstSuccess
    ; Check if auto-startup was selected
    ${If} $AutoStartup == ${BST_CHECKED}
        ; Add to Windows startup registry
        WriteRegStr HKCU "Software\Microsoft\Windows\CurrentVersion\Run" "ACTSE Hardware Bridge" '"$INSTDIR\ACTSE Hardware Bridge.exe" --hidden'
        
        ; Create startup shortcut as backup method
        CreateShortCut "$SMSTARTUP\ACTSE Hardware Bridge.lnk" "$INSTDIR\ACTSE Hardware Bridge.exe" "--hidden" "$INSTDIR\ACTSE Hardware Bridge.exe" 0 SW_SHOWMINIMIZED
    ${EndIf}
    
    ; Create additional shortcuts
    CreateDirectory "$SMPROGRAMS\ACTSE"
    CreateShortCut "$SMPROGRAMS\ACTSE\ACTSE Hardware Bridge.lnk" "$INSTDIR\ACTSE Hardware Bridge.exe" "" "$INSTDIR\ACTSE Hardware Bridge.exe" 0
    CreateShortCut "$SMPROGRAMS\ACTSE\Uninstall ACTSE Hardware Bridge.lnk" "$INSTDIR\Uninstall ACTSE Hardware Bridge.exe"
    
    ; Create desktop shortcut if requested
    CreateShortCut "$DESKTOP\ACTSE Hardware Bridge.lnk" "$INSTDIR\ACTSE Hardware Bridge.exe" "" "$INSTDIR\ACTSE Hardware Bridge.exe" 0
FunctionEnd

; Custom uninstaller function
Function un.onInit
    MessageBox MB_ICONQUESTION|MB_YESNO|MB_DEFBUTTON2 "Are you sure you want to completely remove ACTSE Hardware Bridge and all of its components?" IDYES +2
    Abort
FunctionEnd

; Custom uninstaller success function
Function un.onUninstSuccess
    ; Remove from Windows startup registry
    DeleteRegValue HKCU "Software\Microsoft\Windows\CurrentVersion\Run" "ACTSE Hardware Bridge"
    
    ; Remove startup shortcut
    Delete "$SMSTARTUP\ACTSE Hardware Bridge.lnk"
    
    ; Remove start menu shortcuts
    Delete "$SMPROGRAMS\ACTSE\ACTSE Hardware Bridge.lnk"
    Delete "$SMPROGRAMS\ACTSE\Uninstall ACTSE Hardware Bridge.lnk"
    RMDir "$SMPROGRAMS\ACTSE"
    
    ; Remove desktop shortcut
    Delete "$DESKTOP\ACTSE Hardware Bridge.lnk"
    
    ; Show completion message
    MessageBox MB_ICONINFORMATION "ACTSE Hardware Bridge has been successfully removed from your computer."
FunctionEnd

; Installer sections
Section "MainSection" SEC01
    ; Set output path
    SetOutPath "$INSTDIR"
    
    ; Set overwrite mode
    SetOverwrite on
    
    ; Install files (this will be handled by electron-builder)
    ; The actual file installation is managed by electron-builder
    
    ; Write uninstaller
    WriteUninstaller "$INSTDIR\Uninstall ACTSE Hardware Bridge.exe"
    
    ; Write registry keys for Add/Remove Programs
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\ACTSE Hardware Bridge" "DisplayName" "ACTSE Hardware Bridge"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\ACTSE Hardware Bridge" "UninstallString" "$INSTDIR\Uninstall ACTSE Hardware Bridge.exe"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\ACTSE Hardware Bridge" "DisplayIcon" "$INSTDIR\ACTSE Hardware Bridge.exe"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\ACTSE Hardware Bridge" "Publisher" "ACTSE"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\ACTSE Hardware Bridge" "DisplayVersion" "${VERSION}"
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\ACTSE Hardware Bridge" "NoModify" 1
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\ACTSE Hardware Bridge" "NoRepair" 1
SectionEnd

; Uninstaller sections
Section "Uninstall"
    ; Remove registry keys
    DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\ACTSE Hardware Bridge"
    
    ; Remove files and directories
    RMDir /r "$INSTDIR"
SectionEnd
