<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thai ID Card Reader - Web Interface</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.socket.io/4.7.5/socket.io.min.js"></script>
    <style>
        body { 
            font-family: 'Sarabun', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
        }
        .status-dot { 
            width: 12px; 
            height: 12px; 
            border-radius: 50%; 
            display: inline-block;
            flex-shrink: 0;
        }
        .card-photo {
            max-width: 150px;
            max-height: 200px;
            object-fit: contain;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
        }
        .form-section {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .field-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }
        .log-container {
            background: #1a202c;
            color: white;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            padding: 1rem;
            border-radius: 8px;
            height: 200px;
            overflow-y: auto;
            margin-top: 1rem;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Thai ID Card Reader</h1>
                    <p class="text-gray-600">Web Interface for Card Data</p>
                </div>
                <div class="text-right">
                    <div class="mb-2">
                        <span class="text-sm text-gray-600">Environment:</span>
                        <select id="environmentSelect" class="ml-2 text-sm border border-gray-300 rounded px-2 py-1">
                            <option value="local">Local (ws://localhost:3000)</option>
                            <option value="production">Production (wss://ws.hompok.com)</option>
                        </select>
                        <button id="reconnectBtn" class="ml-2 px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600">
                            Reconnect
                        </button>
                    </div>
                    <div class="mb-2">
                        <span class="text-sm text-gray-600">Bridge ID:</span>
                        <span class="ml-2 text-sm text-gray-800 font-mono">caremat::Registration_Point_1</span>
                    </div>
                    <div class="mb-2">
                        <span class="text-sm text-gray-600">Connection Status:</span>
                        <span id="connectionStatus" class="ml-2 flex items-center">
                            <span class="status-dot bg-red-500 mr-2"></span>
                            <span>Disconnected</span>
                        </span>
                    </div>
                    <div class="mb-2">
                        <span class="text-sm text-gray-600">Reader Status:</span>
                        <span id="readerStatus" class="ml-2 flex items-center">
                            <span class="status-dot bg-gray-400 mr-2"></span>
                            <span>ไม่พบอุปกรณ์</span>
                        </span>
                    </div>
                    <div>
                        <span class="text-sm text-gray-600">Card Status:</span>
                        <span id="cardStatus" class="ml-2 flex items-center">
                            <span class="status-dot bg-gray-400 mr-2"></span>
                            <span>ไม่มีบัตร</span>
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Card Data Form -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">ข้อมูลบัตรประชาชน</h2>
            
            <form id="cardDataForm">
                <!-- Photo Section -->
                <div class="form-section">
                    <h3 class="font-semibold text-gray-700 mb-3">รูปภาพ</h3>
                    <div class="flex justify-center">
                        <img id="cardPhoto" src="" alt="Card Photo" class="card-photo hidden">
                        <div id="photoPlaceholder" class="card-photo flex items-center justify-center bg-gray-200 text-gray-500">
                            ไม่มีรูปภาพ
                        </div>
                    </div>
                </div>

                <!-- Personal Information -->
                <div class="form-section">
                    <h3 class="font-semibold text-gray-700 mb-3">ข้อมูลส่วนตัว</h3>
                    <div class="field-group">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">เลขประจำตัวประชาชน</label>
                            <input type="text" id="citizenId" class="w-full p-2 border border-gray-300 rounded-md" readonly>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">คำนำหน้า (ไทย)</label>
                            <input type="text" id="titleTH" class="w-full p-2 border border-gray-300 rounded-md" readonly>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">ชื่อ (ไทย)</label>
                            <input type="text" id="firstNameTH" class="w-full p-2 border border-gray-300 rounded-md" readonly>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">นามสกุล (ไทย)</label>
                            <input type="text" id="lastNameTH" class="w-full p-2 border border-gray-300 rounded-md" readonly>
                        </div>
                    </div>
                    <div class="field-group">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Title (English)</label>
                            <input type="text" id="titleEN" class="w-full p-2 border border-gray-300 rounded-md" readonly>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">First Name (English)</label>
                            <input type="text" id="firstNameEN" class="w-full p-2 border border-gray-300 rounded-md" readonly>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Last Name (English)</label>
                            <input type="text" id="lastNameEN" class="w-full p-2 border border-gray-300 rounded-md" readonly>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">เพศ</label>
                            <input type="text" id="gender" class="w-full p-2 border border-gray-300 rounded-md" readonly>
                        </div>
                    </div>
                    <div class="field-group">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">วันเกิด</label>
                            <input type="text" id="dob" class="w-full p-2 border border-gray-300 rounded-md" readonly>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">ศาสนา</label>
                            <input type="text" id="religion" class="w-full p-2 border border-gray-300 rounded-md" readonly>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">อายุปัจจุบัน</label>
                            <input type="text" id="currentAge" class="w-full p-2 border border-gray-300 rounded-md" readonly>
                        </div>
                    </div>
                </div>

                <!-- Address Information -->
                <div class="form-section">
                    <h3 class="font-semibold text-gray-700 mb-3">ที่อยู่</h3>
                    <div class="mb-3">
                        <label class="block text-sm font-medium text-gray-700 mb-1">ที่อยู่เต็ม</label>
                        <textarea id="addressFull" class="w-full p-2 border border-gray-300 rounded-md" rows="2" readonly></textarea>
                    </div>
                    <div class="field-group">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">บ้านเลขที่</label>
                            <input type="text" id="houseNo" class="w-full p-2 border border-gray-300 rounded-md" readonly>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">หมู่ที่</label>
                            <input type="text" id="villageNo" class="w-full p-2 border border-gray-300 rounded-md" readonly>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">ซอย</label>
                            <input type="text" id="lane" class="w-full p-2 border border-gray-300 rounded-md" readonly>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">ถนน</label>
                            <input type="text" id="road" class="w-full p-2 border border-gray-300 rounded-md" readonly>
                        </div>
                    </div>
                    <div class="field-group">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">ตำบล</label>
                            <input type="text" id="subDistrict" class="w-full p-2 border border-gray-300 rounded-md" readonly>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">อำเภอ</label>
                            <input type="text" id="district" class="w-full p-2 border border-gray-300 rounded-md" readonly>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">จังหวัด</label>
                            <input type="text" id="province" class="w-full p-2 border border-gray-300 rounded-md" readonly>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">รหัสไปรษณีย์</label>
                            <input type="text" id="postcode" class="w-full p-2 border border-gray-300 rounded-md" readonly>
                        </div>
                    </div>
                </div>

                <!-- Card Information -->
                <div class="form-section">
                    <h3 class="font-semibold text-gray-700 mb-3">ข้อมูลบัตร</h3>
                    <div class="field-group">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">หน่วยงานออกบัตร</label>
                            <input type="text" id="issuer" class="w-full p-2 border border-gray-300 rounded-md" readonly>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">วันที่ออกบัตร</label>
                            <input type="text" id="issueDate" class="w-full p-2 border border-gray-300 rounded-md" readonly>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">วันหมดอายุ</label>
                            <input type="text" id="expireDate" class="w-full p-2 border border-gray-300 rounded-md" readonly>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">อายุบัตร</label>
                            <input type="text" id="cardAge" class="w-full p-2 border border-gray-300 rounded-md" readonly>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-end space-x-4 mt-6">
                    <button type="button" id="clearBtn" class="px-6 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">
                        Clear Data
                    </button>
                    <button type="button" id="copyBtn" class="px-6 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
                        Copy JSON
                    </button>
                    <button type="button" id="saveBtn" class="px-6 py-2 bg-green-500 text-white rounded-md hover:bg-green-600">
                        Save to Database
                    </button>
                </div>
            </form>
        </div>

        <!-- Log Section -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">Connection Log</h2>
            <div id="logContainer" class="log-container">
                <p class="text-gray-400">กำลังเชื่อมต่อ...</p>
            </div>
        </div>
    </div>

    <script src="read.js"></script>
</body>
</html>
