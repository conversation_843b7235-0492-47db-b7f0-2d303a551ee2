# สรุปการอัปเดต Web Interface

## ไฟล์ที่ได้รับการอัปเดต

### 1. `read.html` - หน้าแสดงข้อมูลบัตรประชาชน
✅ **อัปเดตเรียบร้อย**

**การเปลี่ยนแปลง:**
- เปลี่ยน default environment เป็น Production (wss://ws.hompok.com)
- เพิ่มฟิลด์ "อายุเมื่อออกบัตร"
- เพิ่มส่วนแสดงข้อมูลการหมดอายุบัตรแยกต่างหาก:
  - สถานะบัตร
  - ระยะเวลาคงเหลือ/เกินมา
  - จำนวนวันคงเหลือ
  - การแจ้งเตือน

### 2. `read.js` - JavaScript สำหรับ read.html
✅ **อัปเดตเรียบร้อย**

**การเปลี่ยนแปลง:**
- เปลี่ยน default environment เป็น 'production'
- เพิ่มฟิลด์ใหม่ใน formFields object
- เพิ่มฟังก์ชัน `getStatusText()` และ `updateExpiryFieldColors()`
- ปรับปรุงฟังก์ชัน `populateCardData()` ให้รองรับข้อมูลการหมดอายุ
- อัปเดตข้อมูลทดสอบให้ครบถ้วน
- เพิ่มการแสดง log ข้อมูลการหมดอายุ

### 3. `demo-form.html` - แบบฟอร์มลงทะเบียนผู้ป่วย
✅ **อัปเดตเรียบร้อย**

**การเปลี่ยนแปลง:**
- เปลี่ยน default server เป็น Production VPS (wss://ws.hompok.com)
- เพิ่มฟิลด์ใหม่:
  - ศาสนา
  - อายุปัจจุบัน
  - สถานะบัตร
- ปรับปรุงฟังก์ชัน `autoFillForm()` ให้รองรับข้อมูลใหม่
- เพิ่มการเปลี่ยนสีฟิลด์ตามสถานะการหมดอายุ
- ปรับปรุงข้อความแจ้งเตือนให้แสดงข้อมูลการหมดอายุ

## ฟีเจอร์ใหม่

### 🎯 การแสดงข้อมูลการหมดอายุบัตร

**ใน read.html:**
- ส่วนแยกต่างหากสำหรับข้อมูลการหมดอายุ
- การเปลี่ยนสีฟิลด์ตามสถานะ:
  - 🟢 เขียว: บัตรปกติ
  - 🟡 เหลือง: ใกล้หมดอายุ (เหลือ ≤ 6 เดือน)
  - 🔴 แดง: หมดอายุแล้ว
  - 🔵 น้ำเงิน: ไม่มีวันหมดอายุ

**ใน demo-form.html:**
- ฟิลด์ "สถานะบัตร" ที่เปลี่ยนสีตามสถานะ
- การแจ้งเตือนในข้อความ popup

### 🌐 การตั้งค่า Default เป็น VPS

**เปลี่ยนจาก:**
```javascript
environment: 'local'
serverUrl: 'ws://localhost:3000'
```

**เป็น:**
```javascript
environment: 'production'
serverUrl: 'wss://ws.hompok.com'
```

### 📊 ข้อมูลเพิ่มเติม

**ฟิลด์ใหม่ที่รองรับ:**
- อายุเมื่อออกบัตร (ageAtIssueDate)
- ศาสนา (religion)
- อายุปัจจุบัน (currentAge)
- ข้อมูลการหมดอายุครบถ้วน (cardExpiry object)

## การใช้งาน

### 🚀 การเริ่มต้นใช้งาน

1. **เปิดไฟล์ HTML ในเบราว์เซอร์**
   ```
   file:///C:/code/nodejs/actse-hardware-bridge/read.html
   file:///C:/code/nodejs/actse-hardware-bridge/demo-form.html
   ```

2. **ตรวจสอบการเชื่อมต่อ**
   - Connection Status: เขียว = เชื่อมต่อ VPS สำเร็จ
   - Reader Status: เขียว = เครื่องอ่านบัตรพร้อม
   - Card Status: แสดงสถานะบัตร

3. **อ่านบัตรประชาชน**
   - เสียบบัตรในเครื่องอ่าน
   - ระบบจะแสดงข้อมูลอัตโนมัติ
   - ตรวจสอบสถานะการหมดอายุ

### 🧪 การทดสอบ

**ใน read.html:**
- กด `Ctrl+T` เพื่อโหลดข้อมูลทดสอบ
- ข้อมูลทดสอบรวมข้อมูลการหมดอายุแล้ว

**การทดสอบสถานะต่างๆ:**
1. บัตรปกติ: ใช้ข้อมูลทดสอบ default
2. บัตรใกล้หมดอายุ: แก้ไข `daysRemaining` เป็น 150
3. บัตรหมดอายุ: แก้ไข `isExpired: true`, `daysRemaining: -30`

## ตัวอย่างข้อมูลที่แสดง

### ข้อมูลการหมดอายุ

```json
{
  "cardExpiry": {
    "status": "valid",
    "message": "บัตรเหลืออายุอีก 6 ปี 11 เดือน 24 วัน",
    "years": 6,
    "months": 11,
    "days": 24,
    "daysRemaining": 2555,
    "isExpired": false,
    "isNearExpiry": false,
    "expireDate": "2031-08-15"
  }
}
```

### การแสดงผลในหน้าเว็บ

**read.html:**
- สถานะบัตร: "ใช้งานได้"
- ระยะเวลาคงเหลือ: "บัตรเหลืออายุอีก 6 ปี 11 เดือน 24 วัน"
- จำนวนวันคงเหลือ: "2555 วัน"
- การแจ้งเตือน: "✅ ปกติ"

**demo-form.html:**
- สถานะบัตร: "✅ ปกติ" (สีเขียว)
- ข้อความ popup: รวมข้อมูลการหมดอายุ

## การแก้ไขปัญหา

### ❌ ปัญหาที่อาจพบ

1. **ไม่เชื่อมต่อ VPS ได้**
   - ตรวจสอบ internet connection
   - ลองเปลี่ยนเป็น Local environment
   - ตรวจสอบ API Key และ Bridge ID

2. **ไม่แสดงข้อมูลการหมดอายุ**
   - ตรวจสอบว่า Hardware Bridge เป็นเวอร์ชันใหม่
   - ดู Console Log (F12)

3. **สีฟิลด์ไม่เปลี่ยน**
   - Refresh หน้าเว็บ
   - ตรวจสอบ CSS ใน Developer Tools

### ✅ วิธีแก้ไข

1. **Debug ด้วย Console Log**
   ```javascript
   // เปิด F12 และดู Console
   console.log(currentCardData);
   ```

2. **ทดสอบด้วยข้อมูลตัวอย่าง**
   ```javascript
   // กด Ctrl+T ใน read.html
   testCardData();
   ```

3. **เปลี่ยน Environment**
   ```javascript
   // ใน read.html: ใช้ dropdown
   // ใน demo-form.html: แก้ไข CONFIG.serverUrl
   ```

## ไฟล์เอกสาร

📁 **ไฟล์ที่เกี่ยวข้อง:**
- `WEB_INTERFACE_GUIDE.md` - คู่มือการใช้งานแบบละเอียด
- `WEB_INTERFACE_UPDATE_SUMMARY.md` - ไฟล์นี้
- `CARD_EXPIRY_EXAMPLE.md` - ตัวอย่างข้อมูลการหมดอายุ
- `UPDATE_SUMMARY.md` - สรุปการอัปเดตทั้งหมด

## สรุป

✅ **การอัปเดตสำเร็จ:**
- รองรับข้อมูลจากบัตรทั้งหมด รวมข้อมูลการหมดอายุใหม่
- ตั้งค่า default เป็น VPS (wss://ws.hompok.com)
- การแสดงผลที่สวยงามและใช้งานง่าย
- การแจ้งเตือนสถานะบัตรที่ชัดเจน

🎯 **พร้อมใช้งาน:**
- เปิดไฟล์ HTML ในเบราว์เซอร์
- เชื่อมต่อ VPS อัตโนมัติ
- อ่านบัตรและแสดงข้อมูลครบถ้วน
- ตรวจสอบสถานะการหมดอายุได้ทันที
