// read.js - WebSocket client for Thai ID Card Reader

// Configuration - Fixed to VPS server
const CONFIG = {
    // Server URL - Fixed to production VPS
    serverUrl: 'wss://ws.hompok.com',

    // Bridge and authentication settings
    bridgeId: 'caremat::Registration_Point_1', // Should match the bridge ID
    apiKey: 'caremat_secret_key_for_reg_point_1_abcdef123456', // API key for authentication
    reconnectInterval: 5000,
    maxReconnectAttempts: 10
};

// Global variables
let socket = null;
let reconnectAttempts = 0;
let currentCardData = null;

// DOM elements
const connectionStatus = document.getElementById('connectionStatus');
const readerStatus = document.getElementById('readerStatus');
const cardStatus = document.getElementById('cardStatus');
const logContainer = document.getElementById('logContainer');
const cardPhoto = document.getElementById('cardPhoto');
const photoPlaceholder = document.getElementById('photoPlaceholder');

// Form fields
const formFields = {
    citizenId: document.getElementById('citizenId'),
    titleTH: document.getElementById('titleTH'),
    firstNameTH: document.getElementById('firstNameTH'),
    lastNameTH: document.getElementById('lastNameTH'),
    titleEN: document.getElementById('titleEN'),
    firstNameEN: document.getElementById('firstNameEN'),
    lastNameEN: document.getElementById('lastNameEN'),
    gender: document.getElementById('gender'),
    dob: document.getElementById('dob'),
    religion: document.getElementById('religion'),
    currentAge: document.getElementById('currentAge'),
    addressFull: document.getElementById('addressFull'),
    houseNo: document.getElementById('houseNo'),
    villageNo: document.getElementById('villageNo'),
    lane: document.getElementById('lane'),
    road: document.getElementById('road'),
    subDistrict: document.getElementById('subDistrict'),
    district: document.getElementById('district'),
    province: document.getElementById('province'),
    postcode: document.getElementById('postcode'),
    issuer: document.getElementById('issuer'),
    issueDate: document.getElementById('issueDate'),
    expireDate: document.getElementById('expireDate'),
    cardAge: document.getElementById('cardAge'),
    ageAtIssueDate: document.getElementById('ageAtIssueDate'),
    // Card expiry fields
    cardExpiryStatus: document.getElementById('cardExpiryStatus'),
    cardExpiryMessage: document.getElementById('cardExpiryMessage'),
    cardExpiryDays: document.getElementById('cardExpiryDays'),
    cardExpiryAlert: document.getElementById('cardExpiryAlert')
};

// Utility functions
function addLog(message) {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement('p');
    logEntry.innerHTML = `<span style="color: #9ca3af;">[${timestamp}]</span> ${message}`;
    logContainer.appendChild(logEntry);
    logContainer.scrollTop = logContainer.scrollHeight;
    
    // Limit log entries to prevent memory issues
    if (logContainer.children.length > 100) {
        logContainer.removeChild(logContainer.firstChild);
    }
}

function setStatus(element, text, color) {
    const dot = element.querySelector('.status-dot');
    const textSpan = element.querySelector('span:last-child');
    
    dot.className = `status-dot bg-${color}-500 mr-2`;
    if (color === 'yellow') {
        dot.classList.add('animate-pulse');
    }
    textSpan.textContent = text;
}

function clearCardData() {
    currentCardData = null;
    
    // Clear all form fields
    Object.values(formFields).forEach(field => {
        field.value = '';
    });
    
    // Hide photo and show placeholder
    cardPhoto.classList.add('hidden');
    cardPhoto.src = '';
    photoPlaceholder.classList.remove('hidden');
    
    addLog('[INFO] ล้างข้อมูลบัตรแล้ว');
}

function populateCardData(data) {
    currentCardData = data;
    
    // Populate basic fields
    formFields.citizenId.value = data.citizenId || '';
    formFields.titleTH.value = data.titleTH || '';
    formFields.firstNameTH.value = data.firstNameTH || '';
    formFields.lastNameTH.value = data.lastNameTH || '';
    formFields.titleEN.value = data.titleEN || '';
    formFields.firstNameEN.value = data.firstNameEN || '';
    formFields.lastNameEN.value = data.lastNameEN || '';
    formFields.gender.value = data.gender || '';
    formFields.dob.value = data.dob || '';
    formFields.religion.value = data.religion || '';
    formFields.currentAge.value = data.currentAge || '';
    formFields.addressFull.value = data.addressFull || '';
    formFields.issuer.value = data.issuer || '';
    formFields.issueDate.value = data.issueDate || '';
    formFields.expireDate.value = data.expireDate || '';
    formFields.cardAge.value = data.cardAge || '';
    formFields.ageAtIssueDate.value = data.ageAtIssueDate || '';

    // Populate card expiry information
    if (data.cardExpiry) {
        const expiry = data.cardExpiry;
        formFields.cardExpiryStatus.value = getStatusText(expiry.status);
        formFields.cardExpiryMessage.value = expiry.message || '';
        formFields.cardExpiryDays.value = expiry.daysRemaining !== null ?
            `${expiry.daysRemaining} วัน` : 'ไม่มีข้อมูล';

        // Set alert status
        let alertText = 'ปกติ';
        if (expiry.isExpired) {
            alertText = '⚠️ บัตรหมดอายุแล้ว';
        } else if (expiry.isNearExpiry) {
            alertText = '⚠️ ใกล้หมดอายุ';
        } else if (expiry.status === 'never_expires') {
            alertText = '✅ ไม่มีวันหมดอายุ';
        } else {
            alertText = '✅ ปกติ';
        }
        formFields.cardExpiryAlert.value = alertText;

        // Change field colors based on status
        updateExpiryFieldColors(expiry);
    } else {
        // Clear expiry fields if no data
        formFields.cardExpiryStatus.value = '';
        formFields.cardExpiryMessage.value = '';
        formFields.cardExpiryDays.value = '';
        formFields.cardExpiryAlert.value = '';
    }

    // Populate address fields
    if (data.address) {
        formFields.houseNo.value = data.address.houseNo || '';
        formFields.villageNo.value = data.address.villageNo || '';
        formFields.lane.value = data.address.lane || '';
        formFields.road.value = data.address.road || '';
        formFields.subDistrict.value = data.address.subDistrict || '';
        formFields.district.value = data.address.district || '';
        formFields.province.value = data.address.province || '';
        formFields.postcode.value = data.address.postcode || '';
    }
    
    // Handle photo
    if (data.photoBase64) {
        cardPhoto.src = data.photoBase64;
        cardPhoto.classList.remove('hidden');
        photoPlaceholder.classList.add('hidden');
    } else {
        cardPhoto.classList.add('hidden');
        cardPhoto.src = '';
        photoPlaceholder.classList.remove('hidden');
    }
    
    addLog(`[SUCCESS] โหลดข้อมูลบัตรสำเร็จ: ${data.firstNameTH} ${data.lastNameTH}`);

    // Log card expiry information
    if (data.cardExpiry) {
        addLog(`[INFO] ${data.cardExpiry.message}`);
        if (data.cardExpiry.isExpired) {
            addLog(`[WARN] บัตรหมดอายุแล้ว!`);
        } else if (data.cardExpiry.isNearExpiry) {
            addLog(`[WARN] บัตรใกล้หมดอายุ (เหลือ ${data.cardExpiry.daysRemaining} วัน)`);
        }
    }
}

// Helper functions for card expiry
function getStatusText(status) {
    switch (status) {
        case 'valid': return 'ใช้งานได้';
        case 'expired': return 'หมดอายุแล้ว';
        case 'never_expires': return 'ไม่มีวันหมดอายุ';
        default: return 'ไม่ทราบสถานะ';
    }
}

function updateExpiryFieldColors(expiry) {
    const statusField = formFields.cardExpiryStatus;
    const messageField = formFields.cardExpiryMessage;
    const daysField = formFields.cardExpiryDays;
    const alertField = formFields.cardExpiryAlert;

    // Reset classes
    [statusField, messageField, daysField, alertField].forEach(field => {
        field.classList.remove('bg-red-50', 'border-red-300', 'text-red-800');
        field.classList.remove('bg-yellow-50', 'border-yellow-300', 'text-yellow-800');
        field.classList.remove('bg-green-50', 'border-green-300', 'text-green-800');
        field.classList.remove('bg-blue-50', 'border-blue-300', 'text-blue-800');
    });

    // Apply colors based on status
    if (expiry.isExpired) {
        [statusField, messageField, daysField, alertField].forEach(field => {
            field.classList.add('bg-red-50', 'border-red-300', 'text-red-800');
        });
    } else if (expiry.isNearExpiry) {
        [statusField, messageField, daysField, alertField].forEach(field => {
            field.classList.add('bg-yellow-50', 'border-yellow-300', 'text-yellow-800');
        });
    } else if (expiry.status === 'never_expires') {
        [statusField, messageField, daysField, alertField].forEach(field => {
            field.classList.add('bg-blue-50', 'border-blue-300', 'text-blue-800');
        });
    } else {
        [statusField, messageField, daysField, alertField].forEach(field => {
            field.classList.add('bg-green-50', 'border-green-300', 'text-green-800');
        });
    }
}

// WebSocket connection functions
function connectToServer() {
    try {
        addLog(`[INFO] กำลังเชื่อมต่อกับ ${CONFIG.serverUrl}...`);
        setStatus(connectionStatus, 'กำลังเชื่อมต่อ...', 'yellow');
        
        socket = io(CONFIG.serverUrl, {
            transports: ['websocket', 'polling'],
            timeout: 10000,
            forceNew: true,
            auth: {
                apiKey: CONFIG.apiKey,
                clientType: 'monitor', // Identify as monitoring client
                targetBridgeId: CONFIG.bridgeId // Bridge we want to monitor
            }
        });
        
        socket.on('connect', () => {
            addLog(`[SUCCESS] เชื่อมต่อสำเร็จ! Socket ID: ${socket.id}`);
            addLog(`[DEBUG] Connection Type: Monitor Client`);
            addLog(`[DEBUG] Target Bridge ID: ${CONFIG.bridgeId}`);
            addLog(`[DEBUG] Auth: API Key: ${CONFIG.apiKey.substring(0, 10)}...`);
            setStatus(connectionStatus, 'เชื่อมต่อแล้ว (Monitor)', 'green');
            reconnectAttempts = 0;

            // Request current status from bridge
            addLog(`[INFO] กำลังขอสถานะปัจจุบันจาก Bridge...`);
            socket.emit('requestStatus');
        });
        
        socket.on('disconnect', (reason) => {
            addLog(`[WARN] การเชื่อมต่อถูกตัด: ${reason}`);
            setStatus(connectionStatus, 'ตัดการเชื่อมต่อ', 'red');
            setStatus(readerStatus, 'ไม่ทราบสถานะ', 'gray');
            setStatus(cardStatus, 'ไม่ทราบสถานะ', 'gray');
            
            // Auto-reconnect
            if (reason !== 'io client disconnect') {
                setTimeout(connectToServer, CONFIG.reconnectInterval);
            }
        });
        
        socket.on('connect_error', (error) => {
            addLog(`[ERROR] เชื่อมต่อล้มเหลว: ${error.message}`);
            addLog(`[DEBUG] Error details: ${JSON.stringify(error)}`);
            setStatus(connectionStatus, 'เชื่อมต่อล้มเหลว', 'red');

            // Check if it's an authentication error
            if (error.message.includes('Authentication') || error.message.includes('Unauthorized')) {
                addLog(`[ERROR] Authentication failed - check API key and Bridge ID`);
            }

            reconnectAttempts++;
            if (reconnectAttempts < CONFIG.maxReconnectAttempts) {
                addLog(`[INFO] พยายามเชื่อมต่อใหม่ครั้งที่ ${reconnectAttempts}/${CONFIG.maxReconnectAttempts}...`);
                setTimeout(connectToServer, CONFIG.reconnectInterval);
            } else {
                addLog(`[ERROR] ไม่สามารถเชื่อมต่อได้หลังจากพยายาม ${CONFIG.maxReconnectAttempts} ครั้ง`);
            }
        });
        
        // Listen for bridge events - try multiple event names
        socket.on('bridgeEvent', (data) => {
            addLog(`[DEBUG] Received bridgeEvent: ${JSON.stringify(data)}`);
            handleBridgeEvent(data);
        });

        // Listen for monitor-specific events
        socket.on('monitorEvent', (data) => {
            addLog(`[DEBUG] Received monitorEvent: ${JSON.stringify(data)}`);
            handleBridgeEvent(data);
        });

        // Also listen for other possible event names
        socket.on('cardData', (data) => {
            addLog(`[DEBUG] Received cardData: ${JSON.stringify(data)}`);
            handleBridgeEvent(data);
        });

        // Listen for broadcast events
        socket.on('broadcast', (data) => {
            addLog(`[DEBUG] Received broadcast: ${JSON.stringify(data)}`);
            handleBridgeEvent(data);
        });

        // Listen for all events to debug
        socket.onAny((eventName, ...args) => {
            if (eventName !== 'connect' && eventName !== 'disconnect') {
                addLog(`[DEBUG] Received event '${eventName}': ${JSON.stringify(args)}`);
            }
        });
        
    } catch (error) {
        addLog(`[ERROR] เกิดข้อผิดพลาดในการเชื่อมต่อ: ${error.message}`);
        setStatus(connectionStatus, 'เกิดข้อผิดพลาด', 'red');
    }
}

function handleBridgeEvent(data) {
    addLog(`[DEBUG] handleBridgeEvent called with: ${JSON.stringify(data)}`);

    // Handle different data structures
    let eventData = data;

    // If data has a source property, check if it matches our bridge
    if (data.source && data.source !== CONFIG.bridgeId) {
        addLog(`[DEBUG] Ignoring event from different bridge: ${data.source}`);
        return;
    }

    // If no source property, assume it's for us (some servers might not include source)
    if (!data.source) {
        addLog(`[DEBUG] No source property, assuming event is for us`);
    }

    addLog(`[BRIDGE] ${eventData.status}: ${JSON.stringify(eventData.payload || eventData)}`);

    const status = eventData.status || eventData.type || eventData.event;
    const payload = eventData.payload || eventData.data || eventData;

    switch (status) {
        case 'bridge_connected':
            addLog(`[INFO] Bridge is connected and ready`);
            // Don't change reader/card status here, wait for specific status events
            break;

        case 'bridge_disconnected':
            addLog(`[WARN] Bridge is disconnected`);
            setStatus(readerStatus, 'Bridge ไม่เชื่อมต่อ', 'red');
            setStatus(cardStatus, 'ไม่ทราบสถานะ', 'gray');
            break;

        case 'reader_connected':
            setStatus(readerStatus, `เชื่อมต่อแล้ว (${payload.readerName || 'Unknown'})`, 'green');
            break;

        case 'reader_disconnected':
            setStatus(readerStatus, `อุปกรณ์ถูกถอด (${payload.readerName || 'Unknown'})`, 'red');
            setStatus(cardStatus, 'ไม่มีบัตร', 'gray');
            break;

        case 'reading':
            setStatus(cardStatus, 'กำลังอ่านข้อมูล...', 'yellow');
            break;

        case 'card_read_success': {
            setStatus(cardStatus, 'อ่านข้อมูลสำเร็จ', 'green');
            // Handle different payload structures
            const cardData = payload.data || payload;
            if (cardData && cardData.citizenId) {
                populateCardData(cardData);
            } else {
                addLog(`[WARN] No card data found in payload: ${JSON.stringify(payload)}`);
            }
            break;
        }

        case 'card_read_error':
            setStatus(cardStatus, 'อ่านบัตรล้มเหลว', 'red');
            addLog(`[ERROR] ${payload.message || payload}`);
            break;

        case 'card_removed':
            setStatus(cardStatus, 'นำบัตรออกแล้ว', 'gray');
            break;

        default:
            addLog(`[DEBUG] Unknown event status: ${status}`);
    }
}

// Connection handlers - Auto-connect to production VPS only

// Button event handlers
document.getElementById('clearBtn').addEventListener('click', clearCardData);

document.getElementById('copyBtn').addEventListener('click', () => {
    if (currentCardData) {
        navigator.clipboard.writeText(JSON.stringify(currentCardData, null, 2))
            .then(() => addLog('[INFO] คัดลอก JSON ข้อมูลแล้ว'))
            .catch(err => addLog(`[ERROR] ไม่สามารถคัดลอกได้: ${err.message}`));
    } else {
        addLog('[WARN] ไม่มีข้อมูลบัตรให้คัดลอก');
    }
});

document.getElementById('saveBtn').addEventListener('click', () => {
    if (currentCardData) {
        // This is where you would implement saving to your database
        // For now, just show the data that would be saved
        addLog('[INFO] ข้อมูลที่จะบันทึก:');
        addLog(JSON.stringify(currentCardData, null, 2));

        // Example: Send to your API endpoint
        // fetch('/api/save-card-data', {
        //     method: 'POST',
        //     headers: { 'Content-Type': 'application/json' },
        //     body: JSON.stringify(currentCardData)
        // });

        alert('ฟีเจอร์บันทึกข้อมูลยังไม่ได้เชื่อมต่อกับฐานข้อมูล\nกรุณาดูข้อมูลใน Console Log');
    } else {
        addLog('[WARN] ไม่มีข้อมูลบัตรให้บันทึก');
    }
});

// Add test function for debugging (remove in production)
function testCardData() {
    const testData = {
        citizenId: '2500900003237',
        titleTH: 'นาย',
        firstNameTH: 'อภิวัฒน์',
        lastNameTH: 'ชาววิไล',
        titleEN: 'Mr.',
        firstNameEN: 'Aphiwat',
        lastNameEN: 'Chawilai',
        dob: '12/02/2528',
        gender: 'ชาย',
        religion: '01',
        issuer: 'อำเภอเมืองเชียงใหม่/เชียงใหม่',
        issueDate: '15/08/2567',
        expireDate: '15/08/2574',
        currentAge: '39 ปี 5 เดือน 9 วัน',
        ageAtIssueDate: '39 ปี 5 เดือน 3 วัน',
        cardAge: '0 ปี 0 เดือน 6 วัน',
        address: {
            houseNo: '333/5',
            villageNo: 'หมู่ที่ 7',
            lane: '',
            road: '',
            building: '',
            subDistrict: 'แม่เหียะ',
            district: 'เมืองเชียงใหม่',
            province: 'เชียงใหม่',
            postcode: '50100'
        },
        addressFull: '333/5 หมู่ที่ 7 ตำบลแม่เหียะ อำเภอเมืองเชียงใหม่ จังหวัดเชียงใหม่',
        cardExpiry: {
            status: 'valid',
            message: 'บัตรเหลืออายุอีก 6 ปี 11 เดือน 24 วัน',
            years: 6,
            months: 11,
            days: 24,
            daysRemaining: 2555,
            isExpired: false,
            isNearExpiry: false,
            expireDate: '2031-08-15'
        },
        photoBase64: null
    };

    addLog('[TEST] Testing with sample card data');
    populateCardData(testData);
}

// Add keyboard shortcut for testing (Ctrl+T)
document.addEventListener('keydown', (e) => {
    if (e.ctrlKey && e.key === 't') {
        e.preventDefault();
        testCardData();
    }
});

// Initialize connection when page loads
document.addEventListener('DOMContentLoaded', () => {
    addLog('[INFO] เริ่มต้นระบบ Thai ID Card Reader Web Interface');
    addLog(`[INFO] เชื่อมต่อกับ VPS Server: ${CONFIG.serverUrl}`);

    connectToServer();
});

// Handle page unload
window.addEventListener('beforeunload', () => {
    if (socket) {
        socket.disconnect();
    }
});
