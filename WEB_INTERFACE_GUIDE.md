# คู่มือการใช้งาน Web Interface

## ไฟล์ที่อัปเดต

### 1. `read.html` - หน้าแสดงข้อมูลบัตรประชาชนแบบเต็ม

**ฟีเจอร์ใหม่:**
- แสดงข้อมูลการหมดอายุบัตรแบบครบถ้วน
- เปลี่ยน default environment เป็น Production (VPS)
- เพิ่มฟิลด์ "อายุเมื่อออกบัตร"
- ส่วนแสดงข้อมูลการหมดอายุบัตรแยกต่างหาก
- การแจ้งเตือนด้วยสีตามสถานะบัตร

**ข้อมูลที่แสดง:**
- ข้อมูลส่วนตัวครบถ้วน (ไทย/อังกฤษ)
- ที่อยู่แบบละเอียด
- รูปภาพจากบัตร
- **ข้อมูลการหมดอายุ:**
  - สถานะบัตร (ใช้งานได้/หมดอายุ/ไม่มีวันหมดอายุ)
  - ระยะเวลาคงเหลือ/เกินมา
  - จำนวนวันคงเหลือ
  - การแจ้งเตือน (ปกติ/ใกล้หมดอายุ/หมดอายุแล้ว)

### 2. `demo-form.html` - แบบฟอร์มลงทะเบียนผู้ป่วย

**ฟีเจอร์ใหม่:**
- เปลี่ยน default server เป็น Production VPS
- เพิ่มฟิลด์ศาสนา, อายุปัจจุบัน, สถานะบัตร
- การแจ้งเตือนสถานะบัตรในแบบฟอร์ม
- เปลี่ยนสีฟิลด์ตามสถานะการหมดอายุ

**การใช้งาน:**
1. เปิดไฟล์ในเบราว์เซอร์
2. เสียบบัตรประชาชนในเครื่องอ่าน
3. ระบบจะกรอกข้อมูลอัตโนมัติ
4. ตรวจสอบสถานะบัตร (สีเขียว=ปกติ, สีเหลือง=ใกล้หมดอายุ, สีแดง=หมดอายุ)

### 3. `read.js` - JavaScript สำหรับ read.html

**การปรับปรุง:**
- เปลี่ยน default environment เป็น 'production'
- เพิ่มการจัดการข้อมูลการหมดอายุ
- ฟังก์ชันเปลี่ยนสีฟิลด์ตามสถานะ
- ข้อมูลทดสอบที่ครบถ้วนขึ้น

## การตั้งค่า

### Default Configuration

```javascript
const CONFIG = {
    environment: 'production', // เปลี่ยนจาก 'local' เป็น 'production'
    servers: {
        local: 'ws://localhost:3000',
        production: 'wss://ws.hompok.com' // VPS Server
    },
    bridgeId: 'caremat::Registration_Point_1',
    apiKey: 'caremat_secret_key_for_reg_point_1_abcdef123456'
};
```

### การเปลี่ยน Environment

**ใน read.html:**
- ใช้ dropdown ที่มุมขวาบนเพื่อเปลี่ยนระหว่าง Production และ Local
- กดปุ่ม "Reconnect" เพื่อเชื่อมต่อใหม่

**ใน demo-form.html:**
- แก้ไขค่า `CONFIG.serverUrl` ในโค้ด JavaScript

## ข้อมูลการหมดอายุบัตร

### สถานะบัตร

1. **ใช้งานได้ (Valid)**
   - สีเขียว
   - แสดงระยะเวลาคงเหลือ
   - ข้อความ: "บัตรเหลืออายุอีก X ปี Y เดือน Z วัน"

2. **ใกล้หมดอายุ (Near Expiry)**
   - สีเหลือง
   - เหลือไม่เกิน 6 เดือน (180 วัน)
   - การแจ้งเตือน: "⚠️ ใกล้หมดอายุ"

3. **หมดอายุแล้ว (Expired)**
   - สีแดง
   - แสดงระยะเวลาที่เกินมา
   - ข้อความ: "บัตรหมดอายุแล้ว X ปี Y เดือน Z วัน"
   - การแจ้งเตือน: "⚠️ บัตรหมดอายุแล้ว"

4. **ไม่มีวันหมดอายุ (Never Expires)**
   - สีน้ำเงิน
   - สำหรับบัตรที่มีวันหมดอายุ 99/99/9999
   - การแจ้งเตือน: "✅ ไม่มีวันหมดอายุ"

### ข้อมูลที่แสดง

```json
{
  "cardExpiry": {
    "status": "valid|expired|never_expires",
    "message": "บัตรเหลืออายุอีก 1 ปี 2 เดือน 28 วัน",
    "years": 1,
    "months": 2,
    "days": 28,
    "daysRemaining": 423,
    "isExpired": false,
    "isNearExpiry": false,
    "expireDate": "2024-08-15"
  }
}
```

## การทดสอบ

### ข้อมูลทดสอบ (Ctrl+T ใน read.html)

```javascript
const testData = {
    citizenId: '2500900003237',
    titleTH: 'นาย',
    firstNameTH: 'อภิวัฒน์',
    lastNameTH: 'ชาววิไล',
    // ... ข้อมูลอื่นๆ
    cardExpiry: {
        status: 'valid',
        message: 'บัตรเหลืออายุอีก 6 ปี 11 เดือน 24 วัน',
        years: 6,
        months: 11,
        days: 24,
        daysRemaining: 2555,
        isExpired: false,
        isNearExpiry: false,
        expireDate: '2031-08-15'
    }
};
```

### การทดสอบสถานะต่างๆ

1. **ทดสอบบัตรปกติ**: ใช้ข้อมูลทดสอบ (Ctrl+T)
2. **ทดสอบบัตรใกล้หมดอายุ**: แก้ไข `daysRemaining` เป็น 150
3. **ทดสอบบัตรหมดอายุ**: แก้ไข `isExpired` เป็น true และ `daysRemaining` เป็น -30

## การใช้งานจริง

### ขั้นตอนการใช้งาน

1. **เปิดไฟล์ HTML**
   - `read.html` สำหรับดูข้อมูลแบบเต็ม
   - `demo-form.html` สำหรับแบบฟอร์มลงทะเบียน

2. **ตรวจสอบการเชื่อมต่อ**
   - ดูสถานะ Connection Status (เขียว = เชื่อมต่อแล้ว)
   - ดูสถานะ Reader Status (เขียว = เครื่องอ่านพร้อม)

3. **อ่านบัตร**
   - เสียบบัตรประชาชนในเครื่องอ่าน
   - รอให้ระบบอ่านข้อมูล (สถานะจะเป็น "กำลังอ่านข้อมูล...")
   - ตรวจสอบข้อมูลที่แสดง

4. **ตรวจสอบสถานะบัตร**
   - ดูสีของฟิลด์การหมดอายุ
   - อ่านข้อความแจ้งเตือน
   - ตรวจสอบจำนวนวันคงเหลือ

## การแก้ไขปัญหา

### ปัญหาที่พบบ่อย

1. **ไม่สามารถเชื่อมต่อได้**
   - ตรวจสอบ URL ของ VPS Server
   - ตรวจสอบ API Key และ Bridge ID
   - ลองเปลี่ยนเป็น Local environment

2. **ไม่แสดงข้อมูลการหมดอายุ**
   - ตรวจสอบว่า Hardware Bridge เป็นเวอร์ชันใหม่
   - ดู Console Log เพื่อตรวจสอบข้อมูลที่ได้รับ

3. **สีฟิลด์ไม่เปลี่ยน**
   - ตรวจสอบ CSS classes ใน browser developer tools
   - Refresh หน้าเว็บ

### การ Debug

- กด F12 เพื่อเปิด Developer Tools
- ดู Console Log สำหรับข้อความ debug
- ตรวจสอบ Network tab สำหรับ WebSocket connection
- ใช้ Ctrl+T เพื่อทดสอบด้วยข้อมูลตัวอย่าง

## การปรับแต่ง

### เปลี่ยนสี Theme

แก้ไข CSS classes ในไฟล์ HTML:
- `bg-red-50`, `border-red-300`, `text-red-800` สำหรับสีแดง
- `bg-yellow-50`, `border-yellow-300`, `text-yellow-800` สำหรับสีเหลือง
- `bg-green-50`, `border-green-300`, `text-green-800` สำหรับสีเขียว

### เพิ่มฟิลด์ใหม่

1. เพิ่ม HTML input field
2. เพิ่มใน `formFields` object ใน JavaScript
3. เพิ่มการกรอกข้อมูลใน `populateCardData()` function

### เปลี่ยน Server Configuration

แก้ไขค่าใน `CONFIG` object:
- `serverUrl`: URL ของ WebSocket Server
- `bridgeId`: ID ของ Hardware Bridge
- `apiKey`: API Key สำหรับ authentication
