// modify_websocket.js (Modified WebSocket Server with Monitor Client Support)

require('dotenv').config();

const { Server } = require("socket.io");

const io = new Server(3000, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

const VALID_API_KEYS = JSON.parse(process.env.VALID_API_KEYS_JSON || '{}');

if (Object.keys(VALID_API_KEYS).length === 0) {
    console.warn("คำเตือน: ไม่พบ API Keys ในไฟล์ .env หรือไฟล์ .env ไม่ถูกต้อง");
}

const bridgeConnections = new Map();
const monitorConnections = new Map(); // Track monitor connections
const bridgeStatus = new Map(); // Track current status of each bridge

// Helper function to validate API key
function isValidApiKey(apiKey, bridgeId) {
    const expectedClinicId = VALID_API_KEYS[apiKey];
    const actualClinicId = bridgeId.split('::')[0];
    return expectedClinicId && expectedClinicId === actualClinicId;
}

// Helper function to broadcast to monitors
function broadcastToMonitors(bridgeId, eventName, data) {
    const monitorRoom = `monitors_${bridgeId}`;
    const monitorCount = io.sockets.adapter.rooms.get(monitorRoom)?.size || 0;
    
    if (monitorCount > 0) {
        console.log(`[Broadcast] Sending '${eventName}' to ${monitorCount} monitors of ${bridgeId}`);
        io.to(monitorRoom).emit(eventName, {
            source: bridgeId,
            timestamp: new Date().toISOString(),
            ...data
        });
    }
}

// Middleware สำหรับยืนยันตัวตน (Modified to support monitor clients)
io.use((socket, next) => {
    const { apiKey, clientType, bridgeId, targetBridgeId } = socket.handshake.auth;

    if (!apiKey) {
        return next(new Error("Authentication error: Missing API Key"));
    }

    if (clientType === 'monitor') {
        // Monitor client - wants to watch a bridge
        if (!targetBridgeId) {
            return next(new Error("Authentication error: Monitor client missing targetBridgeId"));
        }

        if (!isValidApiKey(apiKey, targetBridgeId)) {
            console.log(`การยืนยันตัวตนล้มเหลวสำหรับ Monitor Client targeting: ${targetBridgeId}`);
            return next(new Error("Authentication error: Invalid API Key for target bridge"));
        }

        socket.clientType = 'monitor';
        socket.targetBridgeId = targetBridgeId;
        socket.clinicId = targetBridgeId.split('::')[0];
        socket.bridgeId = `monitor_${socket.id}_${targetBridgeId}`;
        
        console.log(`[Monitor Auth] Client wants to monitor bridge: ${targetBridgeId}`);
        next();

    } else {
        // Bridge client - hardware bridge (existing logic)
        if (!bridgeId) {
            return next(new Error("Authentication error: Bridge client missing bridgeId"));
        }

        if (!isValidApiKey(apiKey, bridgeId)) {
            console.log(`การยืนยันตัวตนล้มเหลวสำหรับ Bridge ID: ${bridgeId}`);
            return next(new Error("Authentication error: Invalid API Key or mismatched Clinic ID"));
        }

        socket.clientType = 'bridge';
        socket.clinicId = bridgeId.split('::')[0];
        socket.bridgeId = bridgeId;
        
        next();
    }
});

// ส่วนจัดการการเชื่อมต่อ (Modified to handle both client types)
io.on("connection", (socket) => {
    const { clinicId, bridgeId, clientType, targetBridgeId } = socket;

    if (clientType === 'bridge') {
        // Handle bridge client connection (existing logic with improvements)
        if (bridgeConnections.has(bridgeId)) {
            const oldSocketId = bridgeConnections.get(bridgeId);
            console.warn(`[${clinicId}] - พบการเชื่อมต่อซ้ำซ้อนสำหรับ Bridge ID [${bridgeId}]`);
            const oldSocket = io.sockets.sockets.get(oldSocketId);
            if (oldSocket) {
                console.log(`[${clinicId}] - กำลังตัดการเชื่อมต่อเก่า (Socket ID: ${oldSocketId})`);
                oldSocket.disconnect(true);
            }
        }
        
        console.log(`[${clinicId}] - Bridge [${bridgeId}] เชื่อมต่อแล้วด้วย Socket ID: ${socket.id}`);
        bridgeConnections.set(bridgeId, socket.id);
        socket.join(clinicId);

        // Handle bridge events
        socket.on("bridgeEvent", (message) => {
            // แสดง Log ทั่วไป
            console.log(`[${clinicId}] - ได้รับ 'bridgeEvent' จาก ${message.source} | สถานะ: ${message.status}`);

            // Store current status for this bridge
            if (!bridgeStatus.has(bridgeId)) {
                bridgeStatus.set(bridgeId, {});
            }
            const currentStatus = bridgeStatus.get(bridgeId);

            // Update status based on event type
            switch (message.status) {
                case 'reader_connected':
                    currentStatus.readerStatus = 'connected';
                    currentStatus.readerName = message.payload?.readerName;
                    break;
                case 'reader_disconnected':
                    currentStatus.readerStatus = 'disconnected';
                    currentStatus.readerName = message.payload?.readerName;
                    currentStatus.cardStatus = 'removed'; // Reset card status when reader disconnects
                    break;
                case 'reading':
                    currentStatus.cardStatus = 'reading';
                    break;
                case 'card_read_success':
                    currentStatus.cardStatus = 'read_success';
                    currentStatus.lastCardData = message.payload?.data;
                    break;
                case 'card_read_error':
                    currentStatus.cardStatus = 'read_error';
                    break;
                case 'card_removed':
                    currentStatus.cardStatus = 'removed';
                    break;
            }

            // ตรวจสอบถ้าเป็นข้อมูลบัตรที่อ่านสำเร็จ ให้แสดงรายละเอียด
            if (message.status === 'card_read_success' && message.payload && message.payload.data) {
                console.log('[DATA] ข้อมูลบัตรที่ได้รับ:');
                console.dir(message.payload.data, { depth: null, colors: true });
            } else if (message.payload) {
                console.log('[PAYLOAD]:', message.payload);
            }

            // ส่งต่อข้อความนี้ไปยัง Web App ทั้งหมดที่อยู่ในห้องเดียวกัน (existing behavior)
            socket.to(clinicId).emit("deviceUpdate", message);

            // NEW: Broadcast to all monitors watching this bridge
            broadcastToMonitors(bridgeId, 'bridgeEvent', message);
        });

        // Handle print commands (existing logic)
        socket.on("printCommand", (command) => {
            const { targetBridgeId, payload } = command;
            console.log(`[${clinicId}] - ได้รับ 'printCommand' ให้ส่งไปยัง [${targetBridgeId}]`);
            const targetSocketId = bridgeConnections.get(targetBridgeId);
            if (targetSocketId) {
                io.to(targetSocketId).emit("printCommand", payload);
                console.log(`[${clinicId}] - ส่งคำสั่งไปยัง ${targetBridgeId} (Socket ID: ${targetSocketId}) สำเร็จ`);
            } else {
                console.warn(`[${clinicId}] - ไม่พบ Bridge ที่กำลังเชื่อมต่ออยู่สำหรับ ID: ${targetBridgeId}`);
            }
        });

        // Handle status responses from bridge
        socket.on("statusResponse", (response) => {
            console.log(`[${clinicId}] - Received status response from bridge ${bridgeId}`);

            const { requesterId, readerStatus, cardStatus } = response;

            // Send status to the requesting monitor
            if (requesterId) {
                const requesterSocket = io.sockets.sockets.get(requesterId);
                if (requesterSocket) {
                    // Send reader status
                    if (readerStatus) {
                        requesterSocket.emit('bridgeEvent', {
                            source: bridgeId,
                            status: readerStatus.status === 'connected' ? 'reader_connected' : 'reader_disconnected',
                            payload: { readerName: readerStatus.name || 'Unknown' },
                            timestamp: new Date().toISOString()
                        });
                    }

                    // Send card status
                    if (cardStatus) {
                        let cardEvent = {
                            source: bridgeId,
                            timestamp: new Date().toISOString()
                        };

                        switch (cardStatus.status) {
                            case 'reading':
                                cardEvent.status = 'reading';
                                cardEvent.payload = { message: 'กำลังอ่านข้อมูล...' };
                                break;
                            case 'read_success':
                                cardEvent.status = 'card_read_success';
                                cardEvent.payload = { data: cardStatus.data };
                                break;
                            case 'read_error':
                                cardEvent.status = 'card_read_error';
                                cardEvent.payload = { message: cardStatus.message || 'อ่านบัตรล้มเหลว' };
                                break;
                            case 'removed':
                            default:
                                cardEvent.status = 'card_removed';
                                cardEvent.payload = {};
                                break;
                        }

                        requesterSocket.emit('bridgeEvent', cardEvent);
                    }

                    console.log(`[${clinicId}] - Status response sent to monitor ${requesterId}`);
                }
            }
        });

    } else if (clientType === 'monitor') {
        // Handle monitor client connection (NEW)
        console.log(`[Monitor] Client monitoring bridge: ${targetBridgeId} (Socket ID: ${socket.id})`);
        
        // Join a room for this bridge's monitors
        const monitorRoom = `monitors_${targetBridgeId}`;
        socket.join(monitorRoom);
        
        // Track monitor connection
        if (!monitorConnections.has(targetBridgeId)) {
            monitorConnections.set(targetBridgeId, new Set());
        }
        monitorConnections.get(targetBridgeId).add(socket.id);
        
        // Send current bridge status if bridge is connected
        const bridgeSocketId = bridgeConnections.get(targetBridgeId);
        const currentStatus = bridgeStatus.get(targetBridgeId);

        if (bridgeSocketId) {
            // Bridge is connected, send current status
            socket.emit('bridgeEvent', {
                source: targetBridgeId,
                status: 'bridge_connected',
                payload: { message: 'Bridge is currently connected' },
                timestamp: new Date().toISOString()
            });

            // Send current reader status if available
            if (currentStatus?.readerStatus) {
                setTimeout(() => {
                    socket.emit('bridgeEvent', {
                        source: targetBridgeId,
                        status: currentStatus.readerStatus === 'connected' ? 'reader_connected' : 'reader_disconnected',
                        payload: { readerName: currentStatus.readerName || 'Unknown' },
                        timestamp: new Date().toISOString()
                    });
                }, 100);
            }

            // Send current card status if available
            if (currentStatus?.cardStatus) {
                setTimeout(() => {
                    const cardEvent = {
                        source: targetBridgeId,
                        timestamp: new Date().toISOString()
                    };

                    switch (currentStatus.cardStatus) {
                        case 'reading':
                            cardEvent.status = 'reading';
                            cardEvent.payload = { message: 'กำลังอ่านข้อมูล...' };
                            break;
                        case 'read_success':
                            cardEvent.status = 'card_read_success';
                            cardEvent.payload = { data: currentStatus.lastCardData };
                            break;
                        case 'read_error':
                            cardEvent.status = 'card_read_error';
                            cardEvent.payload = { message: 'อ่านบัตรล้มเหลว' };
                            break;
                        case 'removed':
                        default:
                            cardEvent.status = 'card_removed';
                            cardEvent.payload = {};
                            break;
                    }

                    socket.emit('bridgeEvent', cardEvent);
                }, 200);
            }
        } else {
            socket.emit('bridgeEvent', {
                source: targetBridgeId,
                status: 'bridge_disconnected',
                payload: { message: 'Bridge is not connected' },
                timestamp: new Date().toISOString()
            });
        }

        // Handle status requests from monitor clients
        socket.on("requestStatus", () => {
            console.log(`[Monitor] Status request from ${socket.id} for bridge: ${targetBridgeId}`);

            // Forward status request to the target bridge
            const bridgeSocketId = bridgeConnections.get(targetBridgeId);
            if (bridgeSocketId) {
                const bridgeSocket = io.sockets.sockets.get(bridgeSocketId);
                if (bridgeSocket) {
                    // Send status request to bridge with monitor's socket ID for response
                    bridgeSocket.emit("statusRequest", {
                        requesterId: socket.id,
                        requesterType: 'monitor'
                    });
                    console.log(`[Monitor] Status request forwarded to bridge ${targetBridgeId}`);
                } else {
                    // Bridge socket not found
                    socket.emit('bridgeEvent', {
                        source: targetBridgeId,
                        status: 'bridge_disconnected',
                        payload: { message: 'Bridge is not connected' },
                        timestamp: new Date().toISOString()
                    });
                }
            } else {
                // Bridge not connected
                socket.emit('bridgeEvent', {
                    source: targetBridgeId,
                    status: 'bridge_disconnected',
                    payload: { message: 'Bridge is not connected' },
                    timestamp: new Date().toISOString()
                });
            }
        });
    }

    // Handle disconnection (Modified to handle both client types)
    socket.on("disconnect", () => {
        if (clientType === 'bridge') {
            if (bridgeId && bridgeConnections.get(bridgeId) === socket.id) {
                bridgeConnections.delete(bridgeId);
                console.log(`[${clinicId}] - Bridge [${bridgeId}] ตัดการเชื่อมต่อแล้ว`);
                
                // Notify monitors that bridge disconnected
                broadcastToMonitors(bridgeId, 'bridgeEvent', {
                    status: 'bridge_disconnected',
                    payload: { message: 'Bridge disconnected' }
                });
            }
        } else if (clientType === 'monitor') {
            console.log(`[Monitor] Client disconnected from monitoring ${targetBridgeId} (Socket ID: ${socket.id})`);
            
            // Remove from monitor tracking
            if (monitorConnections.has(targetBridgeId)) {
                monitorConnections.get(targetBridgeId).delete(socket.id);
                if (monitorConnections.get(targetBridgeId).size === 0) {
                    monitorConnections.delete(targetBridgeId);
                }
            }
        } else {
            // Legacy web app client (for backward compatibility)
            console.log(`[${clinicId}] - Web App Client ตัดการเชื่อมต่อ: ${socket.id}`);
        }
    });
});

// Status endpoint (optional - for debugging)
setInterval(() => {
    const bridgeCount = bridgeConnections.size;
    const monitorCount = Array.from(monitorConnections.values()).reduce((sum, set) => sum + set.size, 0);
    if (bridgeCount > 0 || monitorCount > 0) {
        console.log(`[Status] Active connections - Bridges: ${bridgeCount}, Monitors: ${monitorCount}`);
    }
}, 30000); // Log every 30 seconds

console.log("Socket.IO Server (V4 - with Bridge and Monitor client support) กำลังทำงาน...");
