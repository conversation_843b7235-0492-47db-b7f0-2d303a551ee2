    // test-print.js
    const { io } = require("socket.io-client");

    const socket = io("ws://localhost:3000", {
        // ในการทดสอบจริง อาจจะต้องส่ง auth token ของ Web App ไปด้วย
    });

    socket.on("connect", () => {
        console.log("เชื่อมต่อกับ Server เพื่อทดสอบสำเร็จ!");

        const dummyReceiptData = {
            items: [
                { name: "ตรวจสุขภาพทั่วไป", quantity: 1, price: 1500.00 },
                { name: "ค่าบริการพยาบาล", quantity: 1, price: 100.00 },
                { name: "ยาพาราเซตามอล", quantity: 1, price: 20.00 },
            ],
            total: 1620.00,
            paymentMethod: "เงินสด"
        };

        const printCommand = {
            action: "print",
            payload: {
                targetBridgeId: "caremat::Registration_Point_1", 
                payload: {
                    printerName: "Xprinter_XP-58", // ชื่อเครื่องพิมพ์ที่ต้องการสั่ง
                    jobType: "receipt",
                    data: dummyReceiptData
                }
            }
        };

        console.log("กำลังส่งคำสั่งพิมพ์...");
        socket.emit("printCommand", printCommand);

        setTimeout(() => {
            console.log("ปิดการเชื่อมต่อทดสอบ");
            socket.disconnect();
        }, 1000);
    });

    socket.on("disconnect", () => {
        console.log("ตัดการเชื่อมต่อแล้ว");
    });
    