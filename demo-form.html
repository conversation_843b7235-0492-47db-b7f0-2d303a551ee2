<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo: Auto-Fill Form with Thai ID Card</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.socket.io/4.7.5/socket.io.min.js"></script>
    <style>
        body { 
            font-family: 'Sarabun', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
        }
        .status-dot { 
            width: 8px; 
            height: 8px; 
            border-radius: 50%; 
            display: inline-block;
        }
        .card-reader-status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Card Reader Status Widget -->
    <div class="card-reader-status">
        <div class="text-xs text-gray-600 mb-1">Card Reader Status</div>
        <div id="cardReaderStatus" class="flex items-center text-sm">
            <span class="status-dot bg-gray-400 mr-2"></span>
            <span>Initializing...</span>
        </div>
    </div>

    <div class="container mx-auto px-4 py-8 max-w-4xl">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-2">Patient Registration Form</h1>
            <p class="text-gray-600">แบบฟอร์มลงทะเบียนผู้ป่วย - Auto-fill with Thai ID Card</p>
            <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                <p class="text-sm text-blue-800">
                    <strong>วิธีใช้:</strong> ใส่บัตรประชาชนในเครื่องอ่านบัตร ระบบจะกรอกข้อมูลให้อัตโนมัติ
                </p>
            </div>
        </div>

        <!-- Registration Form -->
        <form id="registrationForm" class="bg-white rounded-lg shadow-sm p-6">
            <!-- Personal Information Section -->
            <div class="mb-8">
                <h2 class="text-lg font-semibold text-gray-800 mb-4 border-b border-gray-200 pb-2">
                    ข้อมูลส่วนตัว
                </h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">เลขประจำตัวประชาชน *</label>
                        <input type="text" id="citizenId" name="citizenId" 
                               class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                               required>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">คำนำหน้า</label>
                        <input type="text" id="titleTH" name="titleTH" 
                               class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">ชื่อ *</label>
                        <input type="text" id="firstNameTH" name="firstNameTH" 
                               class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                               required>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">นามสกุล *</label>
                        <input type="text" id="lastNameTH" name="lastNameTH" 
                               class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                               required>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">เพศ</label>
                        <select id="gender" name="gender" 
                                class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">เลือกเพศ</option>
                            <option value="ชาย">ชาย</option>
                            <option value="หญิง">หญิง</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">วันเกิด</label>
                        <input type="text" id="dob" name="dob"
                               class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="DD/MM/YYYY">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">ศาสนา</label>
                        <input type="text" id="religion" name="religion"
                               class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">อายุปัจจุบัน</label>
                        <input type="text" id="currentAge" name="currentAge"
                               class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               readonly>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">สถานะบัตร</label>
                        <input type="text" id="cardStatus" name="cardStatus"
                               class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               readonly>
                    </div>
                </div>
            </div>

            <!-- Contact Information Section -->
            <div class="mb-8">
                <h2 class="text-lg font-semibold text-gray-800 mb-4 border-b border-gray-200 pb-2">
                    ข้อมูลติดต่อ
                </h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">เบอร์โทรศัพท์ *</label>
                        <input type="tel" id="phone" name="phone" 
                               class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                               required>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">อีเมล</label>
                        <input type="email" id="email" name="email" 
                               class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">ที่อยู่ปัจจุบัน</label>
                    <textarea id="currentAddress" name="currentAddress" rows="3"
                              class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"></textarea>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">ตำบล</label>
                        <input type="text" id="subDistrict" name="subDistrict" 
                               class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">อำเภอ</label>
                        <input type="text" id="district" name="district" 
                               class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">จังหวัด</label>
                        <input type="text" id="province" name="province" 
                               class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">รหัสไปรษณีย์</label>
                        <input type="text" id="postcode" name="postcode" 
                               class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
            </div>

            <!-- Emergency Contact Section -->
            <div class="mb-8">
                <h2 class="text-lg font-semibold text-gray-800 mb-4 border-b border-gray-200 pb-2">
                    ผู้ติดต่อฉุกเฉิน
                </h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">ชื่อผู้ติดต่อฉุกเฉิน</label>
                        <input type="text" id="emergencyContactName" name="emergencyContactName" 
                               class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">เบอร์โทรผู้ติดต่อฉุกเฉิน</label>
                        <input type="tel" id="emergencyContactPhone" name="emergencyContactPhone" 
                               class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
            </div>

            <!-- Submit Buttons -->
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <button type="button" id="clearFormBtn" 
                        class="px-6 py-3 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors">
                    Clear Form
                </button>
                <button type="submit" 
                        class="px-6 py-3 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors">
                    Register Patient
                </button>
            </div>
        </form>
    </div>

    <script>
        // Simple WebSocket integration for auto-filling form - Fixed to VPS
        const CONFIG = {
            serverUrl: 'wss://ws.hompok.com', // Production VPS only
            bridgeId: 'caremat::Registration_Point_1',
            apiKey: 'caremat_secret_key_for_reg_point_1_abcdef123456'
        };

        let socket = null;
        const cardReaderStatus = document.getElementById('cardReaderStatus');

        function setReaderStatus(text, color) {
            const dot = cardReaderStatus.querySelector('.status-dot');
            const textSpan = cardReaderStatus.querySelector('span:last-child');
            
            dot.className = `status-dot bg-${color}-500 mr-2`;
            textSpan.textContent = text;
        }

        function autoFillForm(cardData) {
            // Fill basic information
            document.getElementById('citizenId').value = cardData.citizenId || '';
            document.getElementById('titleTH').value = cardData.titleTH || '';
            document.getElementById('firstNameTH').value = cardData.firstNameTH || '';
            document.getElementById('lastNameTH').value = cardData.lastNameTH || '';
            document.getElementById('gender').value = cardData.gender || '';
            document.getElementById('dob').value = cardData.dob || '';
            document.getElementById('religion').value = cardData.religion || '';
            document.getElementById('currentAge').value = cardData.currentAge || '';

            // Fill card status information
            if (cardData.cardExpiry) {
                let statusText = 'ปกติ';
                if (cardData.cardExpiry.isExpired) {
                    statusText = '⚠️ บัตรหมดอายุแล้ว';
                } else if (cardData.cardExpiry.isNearExpiry) {
                    statusText = '⚠️ ใกล้หมดอายุ';
                } else if (cardData.cardExpiry.status === 'never_expires') {
                    statusText = '✅ ไม่มีวันหมดอายุ';
                } else {
                    statusText = '✅ ปกติ';
                }
                document.getElementById('cardStatus').value = statusText;

                // Change field color based on status
                const cardStatusField = document.getElementById('cardStatus');
                cardStatusField.classList.remove('bg-red-50', 'border-red-300', 'text-red-800');
                cardStatusField.classList.remove('bg-yellow-50', 'border-yellow-300', 'text-yellow-800');
                cardStatusField.classList.remove('bg-green-50', 'border-green-300', 'text-green-800');

                if (cardData.cardExpiry.isExpired) {
                    cardStatusField.classList.add('bg-red-50', 'border-red-300', 'text-red-800');
                } else if (cardData.cardExpiry.isNearExpiry) {
                    cardStatusField.classList.add('bg-yellow-50', 'border-yellow-300', 'text-yellow-800');
                } else {
                    cardStatusField.classList.add('bg-green-50', 'border-green-300', 'text-green-800');
                }
            }

            // Fill address information
            if (cardData.address) {
                document.getElementById('currentAddress').value = cardData.addressFull || '';
                document.getElementById('subDistrict').value = cardData.address.subDistrict || '';
                document.getElementById('district').value = cardData.address.district || '';
                document.getElementById('province').value = cardData.address.province || '';
                document.getElementById('postcode').value = cardData.address.postcode || '';
            }

            // Show success message with card expiry info
            let message = `Auto-filled form with data for: ${cardData.firstNameTH} ${cardData.lastNameTH}`;
            if (cardData.cardExpiry) {
                message += `\n${cardData.cardExpiry.message}`;
                if (cardData.cardExpiry.isExpired) {
                    message += '\n⚠️ หมายเหตุ: บัตรหมดอายุแล้ว กรุณาตรวจสอบ';
                } else if (cardData.cardExpiry.isNearExpiry) {
                    message += '\n⚠️ หมายเหตุ: บัตรใกล้หมดอายุ';
                }
            }
            alert(message);
        }

        // Initialize WebSocket connection
        function initCardReader() {
            socket = io(CONFIG.serverUrl, {
                auth: {
                    apiKey: CONFIG.apiKey,
                    clientType: 'monitor',
                    targetBridgeId: CONFIG.bridgeId
                }
            });
            
            socket.on('connect', () => {
                setReaderStatus('Connected', 'green');
                // Request current status from bridge
                socket.emit('requestStatus');
            });
            
            socket.on('disconnect', () => {
                setReaderStatus('Disconnected', 'red');
            });
            
            socket.on('bridgeEvent', (data) => {
                if (data.source === CONFIG.bridgeId) {
                    switch (data.status) {
                        case 'bridge_connected':
                            setReaderStatus('Bridge Connected', 'green');
                            break;
                        case 'bridge_disconnected':
                            setReaderStatus('Bridge Disconnected', 'red');
                            break;
                        case 'reader_connected':
                            setReaderStatus('Reader Connected', 'green');
                            break;
                        case 'reader_disconnected':
                            setReaderStatus('Reader Disconnected', 'red');
                            break;
                        case 'card_read_success':
                            setReaderStatus('Card Read Success', 'green');
                            if (data.payload && data.payload.data) {
                                autoFillForm(data.payload.data);
                            }
                            break;
                        case 'reading':
                            setReaderStatus('Reading Card...', 'yellow');
                            break;
                        case 'card_removed':
                            setReaderStatus('Card Removed', 'gray');
                            break;
                    }
                }
            });
        }

        // Form handlers
        document.getElementById('clearFormBtn').addEventListener('click', () => {
            document.getElementById('registrationForm').reset();
        });

        document.getElementById('registrationForm').addEventListener('submit', (e) => {
            e.preventDefault();
            alert('Form submitted! (This is just a demo)');
        });

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            setReaderStatus('Connecting...', 'yellow');
            initCardReader();
        });
    </script>
</body>
</html>
